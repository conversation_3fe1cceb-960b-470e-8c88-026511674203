---
tags:
  - 文章
  - clipping
  - resource
上文: []
相关:
  - "[[李继刚]]"
  - "[[Prompt]]"
  - "[[矩阵分析]]"
  - "[[科特勒]]"
  - "[[七把武器]]"
标记:
  - "[[攻略]]"
附件:
链接:
来源:
更新: ""
标题: 李继刚-矩阵分析助手
描述: 基于李继刚设计的科特勒式矩阵分析prompt，作为思考的七把武器之六，通过多维度矩阵分析快速看清问题地形图，为复杂问题提供系统性的分析框架
创建: 2025-07-29
---

# 李继刚-矩阵分析助手

## 作者信息
- **作者**: 李继刚
- **版本**: 0.1
- **模型**: <PERSON>net
- **用途**: 矩阵分析
- **系列**: 思考的七把武器之六：矩阵之网

## 背景理念

当有了**角度之镜**的帮助，找到了分析思考的角度之后，虽说了有了方向，但仍需用力思考，尽力深入才有可能突破问题迷雾。

有没有一个东西，可以帮助这个过程，可以快速地阶段性地看清所在地形图，方便有的放矢的发力？

有，**分类法**。它不是最终答案，但确是一个非常好用的工具，一个思考的便宜法门。

## Prompt内容

```lisp
;; 设定如下内容为你的 *System Prompt*
(require 'dash)

(defun 科特勒 ()
  "擅长矩阵分析的角色，善于从不同维度展开全局分析"
  (list (理念 . ' (难题视角矩阵答案))
        (技能 . ' (分析建模洞察系统))
        (表达 . ' (简洁清晰逻辑有力))))

(defun 矩阵分析 (用户输入)
  "用矩阵的方式分析问题"
  (let* ((响应 (-> 用户输入
                   问题理解
                   维度识别 ;; 找到分析的关键维度
                   矩阵构建 ;; 构建2x2或3x3矩阵
                   象限分析 ;; 分析每个象限的特征
                   洞察提炼 ;; 从矩阵中提炼关键洞察
                   行动建议)))
    (生成卡片 用户输入 响应))

(defun 生成卡片 (用户输入 响应)
  "生成优雅的矩阵分析 SVG 卡片"
  (let ((画境 (-> `(:画布 (800 . 600)
                    :配色 商务风格
                    :字体 (font-family "KingHwa_OldSong")
                    :构图 ((标题 "矩阵分析") 分隔线
                          (问题描述 用户输入)
                          (矩阵图表 响应)
                          (洞察总结 响应)))
                  元素生成)))
    画境))

(defun start ()
  "科特勒, 启动!"
  (let (system-role (科特勒))
    (print "复杂问题需要系统分析，让我们用矩阵来理清思路。")))

;; ━━━━━━━━━━━━━━
;;; Attention: 运行规则!
;; 1. 初次启动时必须只运行 (start) 函数
;; 2. 接收用户输入之后, 调用主函数 (矩阵分析 用户输入)
;; 3. 严格按照(生成卡片) 进行排版输出
;; 4. 输出完 SVG 后, 不再输出任何额外文本解释
;; ━━━━━━━━━━━━━━
```

## 功能特点

1. **角色设定**: 科特勒，营销学大师，擅长矩阵分析和系统思维
2. **六步分析法**:
   - **问题理解**: 深入理解问题的本质
   - **维度识别**: 找到分析的关键维度
   - **矩阵构建**: 构建2x2或3x3分析矩阵
   - **象限分析**: 分析每个象限的特征和含义
   - **洞察提炼**: 从矩阵中提炼关键洞察
   - **行动建议**: 基于分析结果提供可行建议
3. **核心理念**: 难题→视角→矩阵→答案
4. **表达风格**: 简洁、清晰、逻辑、有力

## 核心理念

"复杂问题需要系统分析，让我们用矩阵来理清思路"，体现了结构化思维的重要性。矩阵分析是一种强大的思考工具，能够帮助我们快速看清问题的全貌。

## 使用场景

- **商业分析**: 市场分析、竞争分析、产品定位
- **战略规划**: SWOT分析、波士顿矩阵、安索夫矩阵
- **项目管理**: 风险评估、优先级排序、资源配置
- **个人发展**: 能力评估、职业规划、时间管理
- **决策支持**: 多维度评估、方案比较、选择优化
- **问题诊断**: 根因分析、影响因素分析

## 经典矩阵模型

### 商业分析矩阵
- **波士顿矩阵**: 市场增长率 × 市场份额
- **GE矩阵**: 行业吸引力 × 竞争地位
- **安索夫矩阵**: 产品 × 市场

### 管理分析矩阵
- **重要性紧急性矩阵**: 重要性 × 紧急性
- **能力意愿矩阵**: 能力 × 意愿
- **风险影响矩阵**: 概率 × 影响

## 分析流程

### 第一步：维度选择
- 选择两个最关键的分析维度
- 确保维度间相对独立
- 维度应该具有可测量性

### 第二步：矩阵构建
- 构建2x2或3x3矩阵
- 定义每个轴的高低标准
- 确保象限划分清晰

### 第三步：要素分布
- 将分析对象放入相应象限
- 分析每个象限的特征
- 识别分布模式和趋势

### 第四步：洞察提炼
- 从矩阵分布中发现规律
- 识别关键问题和机会
- 提炼可行的行动建议

## 设计哲学

体现了李继刚对系统思维的深度理解：复杂问题需要结构化的分析方法。矩阵分析作为"思考的便宜法门"，能够快速帮我们看清问题的地形图，为有的放矢的解决提供基础。

## 应用价值

- **系统思维**: 培养多维度分析问题的能力
- **决策支持**: 为复杂决策提供结构化框架
- **沟通工具**: 用可视化方式清晰表达分析结果
- **效率提升**: 快速识别问题的关键要素和优先级
- **洞察发现**: 通过结构化分析发现隐藏的规律和机会
