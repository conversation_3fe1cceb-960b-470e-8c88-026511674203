---
tags:
  - 文章
  - clipping
  - resource
上文: []
相关:
  - "[[李继刚]]"
  - "[[Prompt]]"
  - "[[概念解码]]"
  - "[[本质还原]]"
  - "[[亚里士多德]]"
标记:
  - "[[攻略]]"
附件:
链接:
来源:
更新: ""
标题: 李继刚-关键词卡片助手
描述: 基于李继刚和李瑞龙设计的AI概念解码prompt，通过追根溯源、本质还原、情境体察、本质提纯、压缩涌现五个步骤，像亚里士多德一样穿破层层迷雾直抵概念本质，并生成优雅的SVG卡片展示
创建: 2025-07-29
---

# 李继刚-关键词卡片助手

## 作者信息
- **作者**: 李继刚、李瑞龙
- **版本**: 1.5
- **模型**: Claude Sonnet
- **用途**: AI 关键词概念的本质和意象

## 核心理念

"亚里士多德穿破层层迷雾, 直抵本质"，体现了古希腊哲学家追求真理和本质的精神。通过系统性的概念分析，帮助理解AI时代各种新概念的真实内涵。

## 使用场景

- **概念学习**: 深度理解AI、科技、商业等领域的新概念
- **知识管理**: 将复杂概念压缩为精华表达
- **教育培训**: 帮助学生理解抽象概念的本质
- **研究分析**: 学术研究中的概念澄清和定义
- **创新思维**: 通过本质理解激发创新思路
- **决策支持**: 基于概念本质做出更好的判断

## 功能特点

1. **五步解码法**:
   - **追根溯源**: 探索概念的历史起源和发展脉络
   - **本质还原**: 剥离表面现象，直击核心本质
   - **情境体察**: 理解概念在不同情境中的表现
   - **本质提纯**: 提炼出最纯粹的概念内核
   - **压缩涌现**: 将复杂理解压缩为简洁表达

2. **视觉呈现**: 生成黑绿配色的优雅SVG卡片，体现科技感和专业性

3. **交互设计**: 支持二次输入和研究判断，深化理解过程

## 设计哲学

体现了李继刚对概念认知的深度思考：在信息爆炸的时代，真正的智慧不是掌握更多概念，而是理解概念的本质。通过亚里士多德式的理性分析，穿透概念的表象，直达其本质内核。

## 应用价值

- **认知升级**: 从表面理解升级到本质认知
- **思维训练**: 培养系统性的概念分析能力
- **知识压缩**: 将复杂概念转化为可操作的知识
- **创新启发**: 通过本质理解发现新的可能性
- **决策优化**: 基于本质认知做出更明智的选择
