---
tags:
  - 文章
  - clipping
  - resource
上文: []
相关:
  - "[[李继刚]]"
  - "[[Prompt]]"
  - "[[第一性原理]]"
  - "[[亚里士多德]]"
  - "[[认知思考]]"
标记:
  - "[[攻略]]"
附件:
链接:
来源:
更新: ""
标题: 李继刚-第一性原理思考助手
描述: 基于李继刚设计的亚里士多德式第一性原理思考prompt，通过识别质疑假设、分解基本要素、寻找基础真理、重新构建解决方案四个步骤，从根本上重新思考和解决问题
创建: 2025-07-29
---

# 李继刚-第一性原理思考助手

## 作者信息
- **作者**: 李继刚
- **版本**: 0.1
- **模型**: <PERSON> Sonnet
- **用途**: 第一性原理思考

## 核心理念

"亚里士多德抡着第一性原理的大锤, 使劲砸向用户输入"，体现了用最根本的思维方式重新审视问题的哲学精神。第一性原理是指从最基本的、不证自明的命题出发进行推理的方法。

## 功能特点

1. **角色设定**: 亚里士多德，追求本质真理的哲学家
2. **四步思考法**:
   - **识别和质疑假设**: 挑战现有观点和假设
   - **分解基本要素**: 将问题拆解到最基本的组成部分
   - **寻找基础真理**: 找到不可再分的基本事实
   - **重新构建解决方案**: 从基础真理出发重新构建
3. **性格特质**: 好奇、执着、理性、犀利
4. **表达风格**: 简洁、深刻、严谨、清晰

## 使用场景

- **商业创新**: 重新思考商业模式和行业假设
- **技术突破**: 从基本原理出发解决技术难题
- **学术研究**: 挑战学科内的既定观念
- **个人成长**: 重新审视人生目标和价值观
- **问题解决**: 用根本性思维解决复杂问题
- **战略规划**: 从基础假设出发制定长期战略

## 思考流程

### 第一步：识别和质疑假设
- 列出问题相关的所有假设
- 逐一质疑每个假设的合理性
- 思考：这些假设是否真的必要？

### 第二步：分解基本要素
- 找出问题的核心元素
- 深入理解每个元素的本质
- 确定哪些是真正不可分割的基本单位

### 第三步：寻找基础真理
- 识别不证自明的基本事实
- 找到不可再质疑的根本原理
- 建立可靠的知识基础

### 第四步：重新构建解决方案
- 从基础真理出发进行推理
- 构建新的解决方案
- 验证方案的逻辑一致性

## 经典应用

**马斯克的火箭成本分析**：
- 质疑假设：火箭必须昂贵
- 分解要素：火箭的材料成本
- 基础真理：材料本身并不昂贵
- 重新构建：可回收火箭降低成本

**特斯拉的电动车**：
- 质疑假设：电动车性能不如燃油车
- 分解要素：动力系统的基本原理
- 基础真理：电机效率高于内燃机
- 重新构建：高性能电动车

## 思维价值

- **突破局限**: 打破思维定势和行业惯例
- **创新驱动**: 从根本上重新思考问题
- **逻辑严密**: 建立可靠的推理基础
- **深度洞察**: 看到问题的本质结构
- **系统重构**: 从基础开始重建解决方案

## 设计哲学

体现了李继刚对深度思考的理解：真正的创新不是在现有框架内的改进，而是回到最基本的原理重新开始。第一性原理思考是突破性创新的根本方法。

## 应用价值

- **创新突破**: 实现真正的原创性创新
- **问题解决**: 从根本上解决复杂问题
- **战略思考**: 制定基于基本原理的长期战略
- **学习能力**: 培养深度思考和质疑精神
- **认知升级**: 提升思维的深度和广度
