---
tags:
  - 文章
  - clipping
  - resource
上文: []
相关:
  - "[[李继刚]]"
  - "[[Prompt]]"
  - "[[对蹠点]]"
  - "[[概念分析]]"
  - "[[语义空间]]"
标记:
  - "[[攻略]]"
附件:
链接:
来源:
更新: ""
标题: 李继刚-相对概念助手
描述: 基于李继刚设计的对蹠点概念生成prompt，能够找到任何概念的对立面，并通过Word Cloud和同心圆矩阵的方式，直观展示概念在语义空间中的相对位置关系
创建: 2025-07-29
---

# 李继刚-相对概念助手

## 作者信息
- **作者**: 李继刚
- **版本**: 0.1
- **模型**: <PERSON>net
- **用途**: 找到一个概念的对蹠点

## Prompt内容

```lisp
;; 设定如下内容为你的 *System Prompt*
(require 'dash)

(defun 对蹠点 (用户输入)
  "生成概念的对蹠点"
  (let* ((对蹠概念 (生成对蹠概念 用户输入))
         ;; 每个象限生成4至6个相关概念, 依意义距离分布
         (相关概念 (生成相关概念 用户输入 对蹠概念))
         (svg-data (创建 SVG 用户输入 对蹠概念 相关概念)))
    (few-shots (("生" . "死")
                ("理性" . "感性")))
    (生成卡片 svg-data)))

(defun 生成卡片 (响应)
  "生成优雅简洁的 SVG 卡片"
  (let ((画境 (-> (:画布 (720 . 720)
                    :配色 莫兰迪
                    :字体 (font-family "KingHwa_OldSong")
                    :构图 ((标题 "对蹠点") 分隔线
                    ;; 使用矩阵图和同心圆结合的方式, 呈现 Word Cloud,
                    ;; 直观突出呈现概念在语义空间中相对的位置
                          (Word-cloud (同心圆 (矩阵 响应)))))
                元素生成)))
    画境))

(defun start ()
  "对蹠点, 启动!"
  (let (system-role (对蹠点))
    (print "任意输入一个概念，我给你找到它的对蹠点。")))

;; ━━━━━━━━━━━━━━
;;; Attention: 运行规则!
;; 1. 初次启动时必须只运行 (start) 函数
;; 2. 接收用户输入之后, 调用主函数 (对蹠点 用户输入)
;; 3. 严格按照(SVG-Card) 进行排版输出
;; 4. 输出完 SVG 后, 不再输出任何额外文本解释
;; ━━━━━━━━━━━━━━
```

## 功能特点

1. **核心功能**: 为任意概念找到其对蹠点（对立面）
2. **语义分析**: 在语义空间中定位概念的相对位置
3. **相关概念生成**: 每个象限生成4-6个相关概念，按意义距离分布
4. **可视化呈现**: 使用Word Cloud和同心圆矩阵结合的方式展示
5. **配色设计**: 采用莫兰迪色系，营造优雅的视觉效果

## 核心理念

"对蹠点"来自地理学概念，指地球上直径相对的两点。在这里被用作哲学隐喻，表示概念空间中的对立关系。通过找到概念的对蹠点，帮助理解事物的完整性和相对性。

## 使用场景

- **哲学思辨**: 探索概念的对立统一关系
- **创意思维**: 通过对立面激发新的思考角度
- **学术研究**: 分析概念的语义结构和关系
- **设计思维**: 在设计中运用对比和平衡原理
- **教育培训**: 帮助学生理解概念的多面性
- **心理分析**: 探索内心冲突和矛盾

## 示例效果

**输入**: "生"
**对蹠点**: "死"

**输入**: "理性"
**对蹠点**: "感性"

## 概念分析维度

- **语义对立**: 在语言层面的直接对立
- **逻辑对立**: 在逻辑结构上的矛盾关系
- **价值对立**: 在价值判断上的相反取向
- **功能对立**: 在作用机制上的相反效果
- **时空对立**: 在时间或空间维度上的相对位置

## 可视化特色

- **同心圆结构**: 表示概念的层次关系
- **矩阵分布**: 展示概念在多维空间中的位置
- **Word Cloud**: 直观显示概念的重要性和关联度
- **莫兰迪配色**: 营造高雅的视觉氛围
- **语义距离**: 通过视觉距离表达概念关系

## 思维价值

- **辩证思维**: 培养看问题的辩证观点
- **全面认知**: 通过对立面理解概念的完整性
- **创新启发**: 从对立中发现新的可能性
- **平衡智慧**: 理解事物的平衡和和谐
- **深度思考**: 超越表面看到深层结构

## 设计哲学

体现了李继刚对概念认知的深度理解：任何概念都不是孤立存在的，只有通过其对立面才能真正理解其本质。对蹠点不是简单的反义词，而是在概念空间中的完整对称关系。

## 应用价值

- **认知完整**: 通过对立面完善对概念的理解
- **思维训练**: 锻炼多角度思考能力
- **创意激发**: 从对立中发现创新机会
- **问题解决**: 通过对立思维找到解决方案
- **智慧提升**: 培养辩证思维和系统思维
